import { createColumnHelper } from "@tanstack/react-table"
import type { UserItem } from "@/types/userType"
const columnHelper = createColumnHelper<UserItem>()

const adminUserColums = [
    columnHelper.accessor('name', {
        header: '姓名',
        cell: (info) => info.getValue(),
    }),

    columnHelper.accessor('email', {
        header: '邮箱',
        cell: (info) => info.getValue(),
    }),

    columnHelper.accessor('avatar', {
        header: '头像',
        cell: (info) => (
            <img 
                src={info.getValue()} 
                alt="avatar" 
                className="w-8 h-8 rounded-full"
            />
        ),
    }),

    columnHelper.display({
        id: 'actions',
        header: '操作',
        cell: () => (
            <div className="space-x-2">
                <button className="px-2 py-1 bg-blue-500 text-white rounded">编辑</button>
                <button className="px-2 py-1 bg-red-500 text-white rounded">删除</button>
            </div>
        ),
    }),
]


export default adminUserColums