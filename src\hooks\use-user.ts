import { useUserStore } from "@/store";
import { useMutation, useQuery } from "@tanstack/react-query";
import { signIn, getUserProfile, getUserList } from "@/apis/userService";
import type { UserProfile, UserSignIn } from "@/types/userType";
import { useState } from "react";


const useUserToken = () => {
    const userToken = useUserStore((state) => state.userToken)
    const setUserToken = useUserStore((state) => state.setUserToken)
    const signInMutation = useMutation({
        mutationFn: signIn,
    });

    const sinIn = async (formData: UserSignIn) => {
        try {
            const result = await signInMutation.mutateAsync(formData)
            setUserToken(result.data)

        } catch (e: unknown) {
            console.log('登录错误:', e)
            throw e;
        }
    }

    const clearUserToken = () => setUserToken({
        token: '',
        refresh_token: userToken.refresh_token
    })

    return {
        sinIn,
        userToken,
        clearUserToken
    };

}

const useUserProfile = () => {
    const userProfile = useUserStore((state) => (state.userProfile))
    const setUserProfile = useUserStore((sate) => (sate.setUserProfile))

    const userProfileMutaition = useMutation({
        mutationFn: getUserProfile,
    })

    const getUserInfo = async () => {
        try {
            const result = await userProfileMutaition.mutateAsync();
            setUserProfile(result.data)
        } catch (e: unknown) {
            console.log('登录错误:', e)
            throw e;
        }
    }

    const clearUserInfo = () => setUserProfile({} as UserProfile)

    return {
        getUserInfo,
        userProfile,
        clearUserInfo
    }
}

const useUserList = () => {
    const [currentPage, setCurrentPage] = useState(1);

    const query = useQuery({
        queryKey: ['user-list', currentPage],
        queryFn: async () => {
            try {
                const result = await getUserList(currentPage);

                if (result && result.code === 200 && result.data) {
                    return result.data;
                }
                return {
                    users: [],
                    currentPage: 1,
                    size: 10,
                    totalCount: 0,
                    totalPages: 1
                };
            }
            catch (e) {
                console.error('API Error:', e);
                // 返回默认数据结构而不是 null
                return {
                    users: [],
                    currentPage: 1,
                    size: 10,
                    totalCount: 0,
                    totalPages: 1
                };
            }
        },
        retry: 3,
        staleTime: 10 * 1000,
        placeholderData: (previousData) => previousData,
    })

    // 确保总是返回有效的数据结构
    const defaultData = {
        users: [],
        currentPage: 1,
        size: 10,
        totalCount: 0,
        totalPages: 1
    };

    const data = query.data || defaultData;

    return {
        userListData: data,
        isLoading: query.isLoading,
        error: query.error,
        pagination: {
            totalCount: Number(data.totalCount) || 0,
            totalPages: Number(data.totalPages) || 1,
            currentPage: Number(data.currentPage) || 1,
            setPage: setCurrentPage,
            getPage: currentPage,
        },
        setPage: setCurrentPage,
        getPage: currentPage,
    }
}

export {
    useUserToken,
    useUserProfile,
    useUserList
}