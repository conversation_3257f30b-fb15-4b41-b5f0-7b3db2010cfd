import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'vite-plugin-mock'
import { faker } from "@faker-js/faker";
import mockUsers from './mockUser';
export default [
    {
        url: '/api/authorizations',
        method: 'post',
        response: () => {
            return {
                code: 200,
                data: {
                    token: "fenva",
                    refresh_token: "fjeaf"
                },
            }
        },
    },
    {
        url: '/api/user/profile',
        method: 'get',
        response: () => {
            return {
                code: 200,
                data: {
                    name: faker.person.fullName(),
                    email: faker.internet.email(),
                    avatar: faker.image.avatarGitHub(),
                },
            }
        },
    },
    {
        url: '/api/users',
        method: 'get',
        response: function (opt) {
            const currentPage = Number(opt.query.page) || 1
            const size = 10
            const totalCount = 100


            return {
                code: 200,
                data: {
                    currentPage,
                    size,
                    totalCount,
                    totalPages: Math.ceil(totalCount / size),
                    users: mockUsers.users
                }
            }
        }
    },
] as <PERSON><PERSON><PERSON>ethod[]