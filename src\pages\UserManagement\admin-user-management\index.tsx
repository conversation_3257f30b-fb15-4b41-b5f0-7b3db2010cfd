import { SakuraTable } from '@/components/table';
import adminUserColums from './colums';
import { useUserList } from '@/hooks/use-user';

export default function AdminUserManagement() {
    const { userListData, pagination, isLoading, error, setPage } = useUserList()

    console.log('UserListData:', userListData)
    console.log('Pagination:', pagination)
    console.log('IsLoading:', isLoading)
    console.log('Error:', error)

    return (
        <div className="container mx-auto py-10 h-[calc(100vh-105px)]">
            {isLoading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg">加载中...</div>
                </div>
            ) : error ? (
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg text-red-500">加载失败: {error.message}</div>
                </div>
            ) : (
                <SakuraTable
                    columns={adminUserColums}
                    data={userListData?.users || []}
                    createButtonText='新增用户'
                    searchPlaceholder='搜索名称'
                    searchKey='name'
                    onClickCreate={() => { }}
                    serverPagination={pagination ? {
                        ...pagination,
                        setPage: setPage
                    } : undefined}
                />
            )}
        </div>
    )
}