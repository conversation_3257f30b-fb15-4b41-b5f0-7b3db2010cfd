import { SakuraTable } from '@/components/table';
import adminUserColums from './colums';
import { useUserList } from '@/hooks/use-user';
export default function AdminUserManagement() {
    const { userListData, pagination } = useUserList()
    console.log(userListData)
    return (
        <div className="container mx-auto py-10 h-[calc(100vh-105px)]">
            <SakuraTable
                columns={adminUserColums}
                data={userListData?.users || []}
                createButtonText='新增用户'
                searchPlaceholder='搜索名称'
                searchKey='name'
                onClickCreate={() => { }}
                serverPagination={pagination}
            />
        </div>
    )
}